using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Cubemovement : MonoBehaviour
{
    public Transform LastPosition;
    public Transform OriginalPosition; // Add this to store original position

    private Vector3 originalPos;
    private Vector3 originalRot;
    private bool isMoving = false;

    void Awake()
    {
        // Store original position and rotation when object is created
        originalPos = transform.position;
        originalRot = transform.eulerAngles;
    }

    // Start is called before the first frame update
    void Start()
    {
        if (LastPosition != null)
        {
            StartCoroutine(RotateAndMove());
        }
    }

    // This will be called every time cube becomes active
    void OnEnable()
    {
        if (LastPosition != null && Application.isPlaying && !isMoving)
        {
            // Reset to original position first, then start movement
            ResetToOriginalPosition();
            StartCoroutine(RotateAndMove());
        }
    }
    
    void ResetToOriginalPosition()
    {
        // Reset position and rotation to original values
        if (OriginalPosition != null)
        {
            transform.position = OriginalPosition.position;
            transform.rotation = OriginalPosition.rotation;
        }
        else
        {
            transform.position = originalPos;
            transform.eulerAngles = originalRot;
        }
    }
    
    IEnumerator RotateAndMove()
    {
        isMoving = true;

        // First rotate 90 degrees on Y-axis
        yield return StartCoroutine(RotateYAxis());

        // Then move to last position
        yield return StartCoroutine(MoveToLastPosition());

        // Wait a moment at the last position
        yield return new WaitForSeconds(1f);

        // Then move to exit position (back to original)
        yield return StartCoroutine(MoveToExitPosition());

        isMoving = false;
    }
    
    IEnumerator RotateYAxis()
    {
        Vector3 startRotation = transform.eulerAngles;
        Vector3 targetRotation = startRotation + new Vector3(0, 90, 0);
        float duration = 1f; // 1 second for rotation
        float elapsedTime = 0f;
        
        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / duration;
            
            // Use smooth interpolation
            t = Mathf.SmoothStep(0f, 1f, t);
            
            transform.eulerAngles = Vector3.Lerp(startRotation, targetRotation, t);
            yield return null;
        }
        
        // Ensure we end up exactly at the target rotation
        transform.eulerAngles = targetRotation;
    }
    
    IEnumerator MoveToLastPosition()
    {
        Vector3 startPosition = transform.position;
        Vector3 targetPosition = LastPosition.position;
        float duration = 2f;
        float elapsedTime = 0f;

        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / duration;

            // Use smooth interpolation
            t = Mathf.SmoothStep(0f, 1f, t);

            transform.position = Vector3.Lerp(startPosition, targetPosition, t);
            yield return null;
        }

        // Ensure we end up exactly at the target position
        transform.position = targetPosition;
    }

    IEnumerator MoveToExitPosition()
    {
        Vector3 startPosition = transform.position;
        Vector3 targetPosition;

        // Determine exit position
        if (OriginalPosition != null)
        {
            targetPosition = OriginalPosition.position;
        }
        else
        {
            targetPosition = originalPos;
        }

        float duration = 2f;
        float elapsedTime = 0f;

        while (elapsedTime < duration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / duration;

            // Use smooth interpolation
            t = Mathf.SmoothStep(0f, 1f, t);

            transform.position = Vector3.Lerp(startPosition, targetPosition, t);
            yield return null;
        }

        // Ensure we end up exactly at the exit position
        transform.position = targetPosition;

        // Reset rotation to original as well
        if (OriginalPosition != null)
        {
            transform.rotation = OriginalPosition.rotation;
        }
        else
        {
            transform.eulerAngles = originalRot;
        }
    }
}
