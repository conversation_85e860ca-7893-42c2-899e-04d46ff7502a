using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.UI;
using TMPro;

public class TrollycubepickSystem : MonoBehaviour
{
     public PlayableDirector Hokk;
     public GameObject[] cube;
     public Slider progressSlider;
     public Text percentageText;
     public GameObject completePanel;
     
     private int currentCubeIndex = 0;
     private int totalCubes = 8;
     
    void Start()
    {
        Hokk.Stop();
        
        // Initialize all cubes as inactive
        for(int i = 0; i < cube.Length; i++)
        {
            cube[i].SetActive(false);
        }
        
        // Initialize UI
        if(progressSlider != null)
        {
            progressSlider.maxValue = totalCubes;
            progressSlider.value = 0;
        }
        
        UpdatePercentageText();
        
        if(completePanel != null)
        {
            completePanel.SetActive(false);
        }
    }
   
    void OnTriggerEnter(Collider other)
    {
        if (other.gameObject.tag == "Cube" && currentCubeIndex < totalCubes)
        {
            Hokk.Play();
            other.gameObject.SetActive(false);
         
            StartCoroutine("shake");
        }
    }
    
    IEnumerator shake()
    {
        this.transform.GetChild(0).gameObject.SetActive(true);
        yield return new WaitForSeconds(3.5f);
         this.transform.GetChild(0).gameObject.SetActive(false);
        // Activate the next cube in sequence
        if(currentCubeIndex < cube.Length)
        {
            cube[currentCubeIndex].SetActive(true);
            currentCubeIndex++;
            
            // Update UI
            UpdateProgressUI();
            
            // Check if all cubes are completed
            if(currentCubeIndex >= totalCubes)
            {
                ShowCompletePanel();
            }
        }
        
        this.transform.GetChild(0).gameObject.SetActive(false);      
    }
    
    void UpdateProgressUI()
    {
        // Update slider
        if(progressSlider != null)
        {
            progressSlider.value = currentCubeIndex;
        }
        
        // Update percentage text
        UpdatePercentageText();
    }
    
    void UpdatePercentageText()
    {
        if(percentageText != null)
        {
            float percentage = (float)currentCubeIndex / totalCubes * 100f;
            percentageText.text = percentage.ToString("F0") + "%";
        }
    }
    
    void ShowCompletePanel()
    {
        if(completePanel != null)
        {
            completePanel.SetActive(true);
        }
        
        Debug.Log("All cubes completed!");
    }
    
    // Optional: Reset function for testing
    public void ResetProgress()
    {
        currentCubeIndex = 0;
        
        for(int i = 0; i < cube.Length; i++)
        {
            cube[i].SetActive(false);
        }
        
        UpdateProgressUI();
        
        if(completePanel != null)
        {
            completePanel.SetActive(false);
        }
    }
}
